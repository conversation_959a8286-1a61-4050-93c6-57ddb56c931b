"""CustomMemory 使用示例."""

from custom_memory_inheritance import CustomMemory


def simple_example():
    """简单使用示例."""
    print("📝 CustomMemory 简单使用示例")

    try:
        # 创建Memory实例
        memory = CustomMemory(collection_name="example_memories")

        # 添加记忆
        user_id = "user123"
        result = memory.add("用户喜欢Python编程", user_id=user_id, infer=False)
        print(f"✅ 添加记忆: {result}")

        if result and "results" in result:
            memory_id = result["results"][0]["id"]

            # 搜索记忆
            search_results = memory.search("Python", user_id=user_id)
            print(f"🔍 搜索结果: {search_results}")

            # 获取所有记忆
            all_memories = memory.get_all(user_id=user_id)
            print(f"📋 所有记忆: {all_memories}")

            # 清理测试数据
            print(f"🗑️ 清理测试记忆: {memory_id}")
            if memory.safe_delete(memory_id):
                print("✅ 清理成功")
            else:
                print("⚠️ 清理失败，但不影响功能演示")

    except Exception as e:
        print(f"❌ 示例运行失败: {str(e)[:100]}...")
        print("💡 这可能是环境配置问题，请检查配置")


def advanced_example():
    """高级使用示例，展示自定义客户端."""
    from openai import OpenAI
    from opensearchpy import OpenSearch
    import os
    
    print("🔧 CustomMemory 高级使用示例 - 自定义客户端")
    
    # 创建自定义客户端
    custom_llm_client = OpenAI(
        base_url=os.getenv("CUSTOM_LLM_URL"),
        api_key=os.getenv("CUSTOM_LLM_KEY"),
    )
    
    # 使用自定义客户端创建Memory实例
    memory = CustomMemory(
        llm_client=custom_llm_client,
        collection_name="advanced_memories"
    )
    
    print("✅ 使用自定义客户端创建Memory实例成功")


if __name__ == "__main__":
    simple_example()
    print("\n" + "="*50 + "\n")
    # advanced_example()  # 需要配置自定义环境变量
