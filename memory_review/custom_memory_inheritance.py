import os

import httpx
from dotenv import load_dotenv

from mem0 import Memory
from mem0.configs.base import EmbedderConfig, LlmConfig, MemoryConfig, VectorStoreConfig
from openai import OpenAI
from opensearchpy import OpenSearch

load_dotenv(override=True)


class CustomMemory(Memory):
    """继承自 mem0.Memory 的自定义内存管理器.
    
    直接继承 Memory 类，支持注入自定义客户端实例，
    比组合模式更加优雅和符合 OOP 原则。
    """

    def __init__(
        self,
        llm_client: OpenAI | None = None,
        embedding_client: OpenAI | None = None,
        opensearch_client: OpenSearch | None = None,
        collection_name: str = "mem0_memories",
    ):
        """初始化自定义Memory实例.

        Args:
            llm_client: 用于LLM推理的OpenAI客户端
            embedding_client: 用于生成embeddings的OpenAI客户端
            opensearch_client: OpenSearch客户端
            collection_name: OpenSearch索引名称
        """
        self.custom_llm_client = llm_client or self._create_default_llm_client()
        self.custom_embedding_client = (
            embedding_client or self._create_default_embedding_client()
        )
        self.custom_opensearch_client = (
            opensearch_client or self._create_default_opensearch_client()
        )
        self.collection_name = collection_name

        config = self._create_memory_config()
        super().__init__(config)
        self._inject_custom_clients()

    def _create_default_llm_client(self) -> OpenAI:
        return OpenAI(
            base_url=os.getenv("QWQ_BASE_URL"),
            api_key=os.getenv("QWQ_API_KEY"),
            http_client=httpx.Client(verify=False, timeout=60.0),
        )

    def _create_default_embedding_client(self) -> OpenAI:
        return OpenAI(
            base_url=os.getenv("EMBEDDING_BASE_URL"),
            api_key=os.getenv("EMBEDDING_API_KEY"),
            http_client=httpx.Client(verify=False, timeout=30.0),
        )

    def _create_default_opensearch_client(self) -> OpenSearch:
        return OpenSearch(
            hosts=[
                {
                    "host": os.getenv("OPENSEARCH_HOST"),
                    "port": int(os.getenv("OPENSEARCH_PORT", "443")),
                }
            ],
            http_auth=(
                os.getenv("OPENSEARCH_USERNAME"),
                os.getenv("OPENSEARCH_PASSWORD"),
            ),
            use_ssl=True,
            verify_certs=False,
            ssl_assert_hostname=False,
            ssl_show_warn=False,
            pool_maxsize=20,
        )

    def _create_memory_config(self) -> MemoryConfig:
        llm_config = LlmConfig(
            provider="openai",
            config={
                "model": os.getenv("MODEL_NAME", "gpt-4o-mini"),
                "api_key": self.custom_llm_client.api_key,
                "openai_base_url": str(self.custom_llm_client.base_url),
            },
        )

        embedder_config = EmbedderConfig(
            provider="openai",
            config={
                "model": os.getenv("EMBEDDING_MODEL", "text-embedding-3-small"),
                "api_key": self.custom_embedding_client.api_key,
                "openai_base_url": str(self.custom_embedding_client.base_url),
                "embedding_dims": None,
                "output_dimensionality": None,
            },
        )

        vector_store_config = VectorStoreConfig(
            provider="opensearch",
            config={
                "host": os.getenv("OPENSEARCH_HOST", "localhost"),
                "port": int(os.getenv("OPENSEARCH_PORT", "443")),
                "http_auth": (
                    os.getenv("OPENSEARCH_USERNAME", "admin"),
                    os.getenv("OPENSEARCH_PASSWORD", "admin"),
                ),
                "collection_name": self.collection_name,
                "verify_certs": False,
                "use_ssl": True,
            },
        )

        return MemoryConfig(
            llm=llm_config,
            embedder=embedder_config,
            vector_store=vector_store_config,
            version="v0.1",
        )

    def _inject_custom_clients(self):
        """注入自定义客户端到Memory实例中."""
        if hasattr(self, "llm") and hasattr(self.llm, "client"):
            self.llm.client = self.custom_llm_client

        if hasattr(self, "embedding_model") and hasattr(self.embedding_model, "client"):
            self.embedding_model.client = self.custom_embedding_client

            # 清理可能导致维度问题的配置
            if hasattr(self.embedding_model, "config"):
                config = self.embedding_model.config
                # 清理维度相关参数
                for attr in ["embedding_dims", "output_dimensionality", "dimensions"]:
                    if hasattr(config, attr):
                        setattr(config, attr, None)

        if hasattr(self, "vector_store") and hasattr(self.vector_store, "client"):
            self.vector_store.client = self.custom_opensearch_client

            # 确保 vector store 使用正确的 embedding 维度
            if hasattr(self.vector_store, "embedding_model_dims"):
                # 获取实际的 embedding 维度
                try:
                    test_embedding = self.embedding_model.embed("test")
                    if test_embedding:
                        actual_dims = len(test_embedding)
                        self.vector_store.embedding_model_dims = actual_dims
                        print(f"   🔧 设置 vector store 维度为: {actual_dims}")

                        # 尝试删除可能存在的错误索引
                        try:
                            self._ensure_correct_index()
                        except Exception as e:
                            print(f"   ⚠️ 索引检查警告: {str(e)[:50]}...")
                except:
                    # 如果无法获取，使用默认值
                    self.vector_store.embedding_model_dims = 1024

    def _ensure_correct_index(self):
        """确保 OpenSearch 索引配置正确."""
        try:
            client = self.vector_store.client
            index_name = self.collection_name

            # 检查索引是否存在
            if client.indices.exists(index=index_name):
                # 获取当前索引映射
                mapping = client.indices.get_mapping(index=index_name)
                current_mapping = mapping.get(index_name, {}).get('mappings', {})

                # 检查 vector_field 的维度配置
                vector_field_props = current_mapping.get('properties', {}).get('vector_field', {})
                current_dims = vector_field_props.get('dimension')
                expected_dims = self.vector_store.embedding_model_dims

                if current_dims and current_dims != expected_dims:
                    print(f"   🔧 检测到维度不匹配: 当前={current_dims}, 期望={expected_dims}")
                    print(f"   🗑️ 删除旧索引: {index_name}")
                    client.indices.delete(index=index_name)
                    print(f"   ✅ 旧索引已删除，系统将自动创建新索引")

        except Exception as e:
            # 索引操作失败不影响主要功能
            print(f"   ⚠️ 索引操作失败: {str(e)[:50]}...")

    def safe_delete(self, memory_id: str, verbose: bool = False, retry_count: int = 2):
        """安全的删除方法，绕过 mem0 的删除 bug."""
        import time

        try:
            client = self.vector_store.client
            index_name = self.collection_name

            # 刷新索引以确保最新数据可见
            try:
                client.indices.refresh(index=index_name)
            except:
                pass  # 刷新失败不影响主要功能

            if verbose:
                # 只在详细模式下显示文档结构
                print(f"   🔍 查看索引 {index_name} 中的文档结构...")
                all_docs = client.search(index=index_name, body={"query": {"match_all": {}}, "size": 3})

                if all_docs.get('hits', {}).get('hits'):
                    print("   📋 找到的文档示例:")
                    for i, hit in enumerate(all_docs['hits']['hits'][:2]):
                        print(f"      文档 {i+1}: _id={hit['_id']}")
                        source = hit.get('_source', {})
                        print(f"      字段: {list(source.keys())}")
                        if 'id' in source:
                            print(f"      id字段值: {source['id']}")

            # 优化的搜索策略：按成功率排序
            search_strategies = [
                ("match查询", {"match": {"id": memory_id}}),
                ("keyword精确匹配", {"term": {"id.keyword": memory_id}}),
                ("普通term查询", {"term": {"id": memory_id}}),
                ("通配符查询", {"wildcard": {"id": f"*{memory_id}*"}}),
            ]

            # 重试机制处理索引延迟
            for attempt in range(retry_count):
                if attempt > 0:
                    print(f"   🔄 第 {attempt + 1} 次尝试删除...")
                    time.sleep(1)  # 等待索引更新
                    try:
                        client.indices.refresh(index=index_name)
                    except:
                        pass

                for strategy_name, query in search_strategies:
                    try:
                        search_body = {"query": query}
                        response = client.search(index=index_name, body=search_body)
                        hits = response.get('hits', {}).get('hits', [])

                        if hits:
                            print(f"   ✅ 使用{strategy_name}找到 {len(hits)} 个匹配文档")
                            # 删除找到的文档
                            deleted_count = 0
                            for hit in hits:
                                doc_id = hit['_id']
                                client.delete(index=index_name, id=doc_id)
                                deleted_count += 1

                            print(f"   ✅ 成功删除 {deleted_count} 个文档")
                            return True
                        elif verbose and attempt == 0:
                            print(f"   ❌ {strategy_name}未找到匹配文档")

                    except Exception as search_error:
                        if verbose:
                            print(f"   ⚠️ {strategy_name}搜索失败: {str(search_error)[:50]}...")
                        continue

                # 如果第一次尝试失败，显示重试信息
                if attempt == 0 and retry_count > 1:
                    print(f"   ⏳ 未找到记忆，可能是索引延迟，将重试...")

            print(f"   ⚠️ 经过 {retry_count} 次尝试，未找到记忆: {memory_id}")
            return False

        except Exception as e:
            print(f"   ❌ 安全删除失败: {str(e)[:100]}...")
            return False

    def safe_delete_all(self, user_id: str):
        """安全的删除所有记忆方法."""
        try:
            # 尝试直接从 OpenSearch 删除
            client = self.vector_store.client
            index_name = self.collection_name

            # 搜索用户的所有文档
            search_body = {
                "query": {
                    "term": {
                        "user_id": user_id
                    }
                }
            }

            response = client.search(index=index_name, body=search_body)
            hits = response.get('hits', {}).get('hits', [])

            deleted_count = 0
            for hit in hits:
                doc_id = hit['_id']
                client.delete(index=index_name, id=doc_id)
                deleted_count += 1

            print(f"   ✅ 直接从 OpenSearch 删除 {deleted_count} 条记忆")
            return deleted_count

        except Exception as e:
            print(f"   ❌ 安全删除所有记忆失败: {str(e)[:100]}...")
            return 0

    def cleanup_test_data(self):
        """清理所有测试数据."""
        try:
            client = self.vector_store.client
            index_name = self.collection_name

            # 搜索包含测试关键词的文档
            test_keywords = ["测试", "test", "这是一个测试记忆"]
            deleted_count = 0

            for keyword in test_keywords:
                search_body = {
                    "query": {
                        "match": {
                            "payload": keyword
                        }
                    }
                }

                response = client.search(index=index_name, body=search_body)
                hits = response.get('hits', {}).get('hits', [])

                for hit in hits:
                    doc_id = hit['_id']
                    client.delete(index=index_name, id=doc_id)
                    deleted_count += 1
                    print(f"   🗑️ 删除测试文档: {doc_id}")

            # 也尝试删除最近的几条记录（可能是测试数据）
            recent_docs = client.search(
                index=index_name,
                body={
                    "query": {"match_all": {}},
                    "sort": [{"_id": {"order": "desc"}}],
                    "size": 3
                }
            )

            for hit in recent_docs.get('hits', {}).get('hits', []):
                doc_id = hit['_id']
                try:
                    client.delete(index=index_name, id=doc_id)
                    deleted_count += 1
                    print(f"   🗑️ 删除最近文档: {doc_id}")
                except:
                    pass  # 可能已经删除了

            return deleted_count

        except Exception as e:
            print(f"   ❌ 清理测试数据失败: {str(e)[:100]}...")
            return 0


def demo_basic_functions():
    """演示基本功能的使用方法."""
    print("🚀 CustomMemory 继承方式演示")
    
    # 创建Memory实例
    memory = CustomMemory(collection_name="redline_memories")
    print("✅ CustomMemory 初始化成功！")
    print(f"✅ 类型验证: isinstance(memory, Memory) = {isinstance(memory, Memory)}")
    
    # 展示继承的优势
    print("\n🌟 继承方式的优势:")
    print("   ✅ 直接继承 Memory 类")
    print("   ✅ 无需代理方法")
    print("   ✅ 类型安全")
    print("   ✅ 可以重写任何方法")
    
    # 展示可用的方法
    memory_methods = [
        method
        for method in dir(memory)
        if not method.startswith("_") and callable(getattr(memory, method))
    ]
    print(f"\n📋 可用方法: {', '.join(memory_methods[:8])}...")
    
    # 功能演示（代码示例）
    user_id = "demo_user"
    
    print("\n🔄 功能演示（代码示例）...")
    print("\n📝 1. 添加记忆:")
    print("   result = memory.add('用户是Python开发专家', user_id='demo_user')")
    print("   # 返回: {'results': [{'id': 'memory_id', 'memory': '...'}]}")
    
    print("\n📋 2. 获取所有记忆:")
    print("   all_memories = memory.get_all(user_id='demo_user')")
    print("   # 返回: [{'id': '...', 'memory': '...', 'user_id': '...'}]")
    
    print("\n🔍 3. 搜索记忆:")
    print("   results = memory.search('Python开发', user_id='demo_user')")
    print("   # 返回: {'results': [{'memory': '...', 'score': 0.95}]}")
    
    print("\n📄 4. 获取特定记忆:")
    print("   memory_detail = memory.get('memory_id')")
    print("   # 返回: {'id': '...', 'memory': '...', 'user_id': '...'}") 
    
    print("\n✏️ 5. 更新记忆:")
    print("   result = memory.update('memory_id', '更新后的内容')")
    print("   # 返回: {'message': 'Memory updated successfully'}")
    
    print("\n🗑️ 6. 删除记忆:")
    print("   memory.delete('memory_id')")
    print("   # 删除指定记忆")
    
    print("\n🗑️ 7. 删除用户所有记忆:")
    print("   memory.delete_all(user_id='demo_user')")
    print("   # 删除用户的所有记忆")
    
    # 尝试实际运行一个简单的测试
    print("\n🧪 尝试实际测试...")

    # 首先检查配置
    print("   🔍 检查配置...")
    try:
        # 检查 embedding 配置
        if hasattr(memory, "embedding_model") and hasattr(memory.embedding_model, "config"):
            config = memory.embedding_model.config
            print(f"   📊 Embedding模型: {getattr(config, 'model', 'Unknown')}")

            # 检查维度相关配置
            dims_attrs = ["embedding_dims", "output_dimensionality", "dimensions"]
            for attr in dims_attrs:
                if hasattr(config, attr):
                    value = getattr(config, attr)
                    print(f"   📏 {attr}: {value}")

        # 先测试 embedding 功能
        print("   🔤 测试 embedding 功能...")
        try:
            test_embedding = memory.embedding_model.embed("测试文本")
            if test_embedding and len(test_embedding) > 0:
                print(f"   ✅ Embedding 成功，维度: {len(test_embedding)}")
            else:
                print("   ❌ Embedding 返回空值")
                print("   💡 跳过后续测试，因为 embedding 功能异常")
                return
        except Exception as embed_error:
            print(f"   ❌ Embedding 测试失败: {str(embed_error)[:100]}...")
            print("   💡 跳过后续测试，因为 embedding 功能异常")
            return

        # 尝试基本功能测试
        print("   🧪 开始基本功能测试...")
        test_result = memory.add("这是一个测试记忆", user_id=user_id, infer=False)

        if test_result and "results" in test_result:
            memory_id = test_result["results"][0]["id"]
            print(f"   ✅ 添加测试记忆成功: {memory_id}")

            # 立即清理（使用安全删除方法）
            print(f"   🗑️ 清理测试记忆: {memory_id}")
            try:
                # 直接使用安全删除方法，避免 mem0 的 bug
                if memory.safe_delete(memory_id):
                    print("   ✅ 清理测试记忆成功")
                else:
                    print("   🔄 尝试清理最近的测试数据...")
                    deleted_count = memory.cleanup_test_data()
                    if deleted_count > 0:
                        print(f"   ✅ 清理了 {deleted_count} 条测试数据")
                    else:
                        print("   💡 添加功能正常，跳过清理步骤")
            except Exception as delete_error:
                print(f"   ⚠️ 清理失败: {str(delete_error)[:50]}...")
                print("   💡 添加功能正常，清理步骤可选")

            print("\n🎉 基本功能测试通过！")
        else:
            print("   ❌ 测试失败：返回格式异常")
            print(f"   📋 返回结果: {test_result}")

    except Exception as e:
        error_msg = str(e)
        print(f"   ❌ 实际测试失败: {error_msg[:150]}...")

        # 提供具体的解决建议
        print("\n   💡 可能的解决方案:")
        if "matryoshka" in error_msg.lower():
            print("   🔧 Embedding模型维度问题:")
            print("      - 当前模型不支持动态维度调整")
            print("      - 建议更换为 text-embedding-3-small 或其他兼容模型")
            print("      - 或者在环境变量中设置 EMBEDDING_MODEL=text-embedding-3-small")
        elif "vector_field" in error_msg or "mapper_parsing_exception" in error_msg:
            print("   🗄️ OpenSearch 索引问题:")
            print("      - 索引的 vector_field 配置与 embedding 维度不匹配")
            print("      - 建议删除现有索引，让系统重新创建")
            print("      - 或者手动创建正确维度的索引")
            print(f"      - 当前 embedding 维度: 1024")
        elif "connection" in error_msg.lower() or "timeout" in error_msg.lower():
            print("   🌐 网络连接问题:")
            print("      - 检查网络连接")
            print("      - 检查服务地址和端口配置")
        elif "auth" in error_msg.lower() or "401" in error_msg or "403" in error_msg:
            print("   🔑 认证问题:")
            print("      - 检查API密钥是否正确")
            print("      - 检查用户名和密码配置")
        else:
            print("   🔧 通用解决方案:")
            print("      - 检查所有环境变量配置")
            print("      - 确保所有服务正常运行")
            print("      - 检查防火墙和网络策略")

        print("\n   📝 注意: 继承方式的代码结构是正确的，问题通常出在环境配置上")
    
    print("\n📝 使用提示:")
    print("   # 基本使用方法")
    print("   memory = CustomMemory(collection_name='my_memories')")
    print("   result = memory.add('用户信息', user_id='user123')")
    print("   memories = memory.search('查询内容', user_id='user123')")
    print("   all_memories = memory.get_all(user_id='user123')")


if __name__ == "__main__":
    demo_basic_functions()
