"""CustomMemory 演示模块."""

from mem0 import Memory
from custom_memory_inheritance import CustomMemory


def demo_basic_functions():
    """演示基本功能的使用方法."""
    print("🚀 CustomMemory 继承方式演示")
    
    # 创建Memory实例
    memory = CustomMemory(collection_name="redline_memories")
    print("✅ CustomMemory 初始化成功！")
    print(f"✅ 类型验证: isinstance(memory, Memory) = {isinstance(memory, Memory)}")
    
    # 展示继承的优势
    print("\n🌟 继承方式的优势:")
    print("   ✅ 直接继承 Memory 类")
    print("   ✅ 无需代理方法")
    print("   ✅ 类型安全")
    print("   ✅ 可以重写任何方法")
    print("   ✅ 模块化设计，核心功能分离")
    
    # 展示可用的方法
    memory_methods = [
        method
        for method in dir(memory)
        if not method.startswith("_") and callable(getattr(memory, method))
    ]
    print(f"\n📋 可用方法: {', '.join(memory_methods[:8])}...")
    
    # 功能演示（代码示例）
    user_id = "demo_user"
    
    print("\n🔄 功能演示（代码示例）...")
    print("\n📝 1. 添加记忆:")
    print("   result = memory.add('用户是Python开发专家', user_id='demo_user')")
    print("   # 返回: {'results': [{'id': 'memory_id', 'memory': '...'}]}")
    
    print("\n📋 2. 获取所有记忆:")
    print("   all_memories = memory.get_all(user_id='demo_user')")
    print("   # 返回: [{'id': '...', 'memory': '...', 'user_id': '...'}]")
    
    print("\n🔍 3. 搜索记忆:")
    print("   results = memory.search('Python开发', user_id='demo_user')")
    print("   # 返回: {'results': [{'memory': '...', 'score': 0.95}]}")
    
    print("\n📄 4. 获取特定记忆:")
    print("   memory_detail = memory.get('memory_id')")
    print("   # 返回: {'id': '...', 'memory': '...', 'user_id': '...'}") 
    
    print("\n✏️ 5. 更新记忆:")
    print("   result = memory.update('memory_id', '更新后的内容')")
    print("   # 返回: {'message': 'Memory updated successfully'}")
    
    print("\n🗑️ 6. 删除记忆:")
    print("   memory.delete('memory_id')")
    print("   # 删除指定记忆")
    
    print("\n🗑️ 7. 安全删除记忆 (推荐):")
    print("   memory.safe_delete('memory_id')")
    print("   # 使用改进的删除方法，避免 mem0 的 bug")
    
    print("\n🗑️ 8. 删除用户所有记忆:")
    print("   memory.safe_delete_all(user_id='demo_user')")
    print("   # 删除用户的所有记忆")
    
    # 尝试实际运行一个简单的测试
    print("\n🧪 尝试实际测试...")
    _run_actual_test(memory, user_id)
    
    print("\n📝 使用提示:")
    print("   # 基本使用方法")
    print("   memory = CustomMemory(collection_name='my_memories')")
    print("   result = memory.add('用户信息', user_id='user123')")
    print("   memories = memory.search('查询内容', user_id='user123')")
    print("   all_memories = memory.get_all(user_id='user123')")


def _run_actual_test(memory, user_id):
    """运行实际测试."""
    # 首先检查配置
    print("   🔍 检查配置...")
    try:
        # 检查 embedding 配置
        if hasattr(memory, "embedding_model") and hasattr(memory.embedding_model, "config"):
            config = memory.embedding_model.config
            print(f"   📊 Embedding模型: {getattr(config, 'model', 'Unknown')}")

            # 检查维度相关配置
            dims_attrs = ["embedding_dims", "output_dimensionality", "dimensions"]
            for attr in dims_attrs:
                if hasattr(config, attr):
                    value = getattr(config, attr)
                    print(f"   📏 {attr}: {value}")

        # 先测试 embedding 功能
        print("   🔤 测试 embedding 功能...")
        try:
            test_embedding = memory.embedding_model.embed("测试文本")
            if test_embedding and len(test_embedding) > 0:
                print(f"   ✅ Embedding 成功，维度: {len(test_embedding)}")
            else:
                print("   ❌ Embedding 返回空值")
                print("   💡 跳过后续测试，因为 embedding 功能异常")
                return
        except Exception as embed_error:
            print(f"   ❌ Embedding 测试失败: {str(embed_error)[:100]}...")
            print("   💡 跳过后续测试，因为 embedding 功能异常")
            return

        # 尝试基本功能测试
        print("   🧪 开始基本功能测试...")
        test_result = memory.add("这是一个测试记忆", user_id=user_id, infer=False)

        if test_result and "results" in test_result:
            memory_id = test_result["results"][0]["id"]
            print(f"   ✅ 添加测试记忆成功: {memory_id}")

            # 立即清理（使用安全删除方法）
            print(f"   🗑️ 清理测试记忆: {memory_id}")
            try:
                # 直接使用安全删除方法，避免 mem0 的 bug
                if memory.safe_delete(memory_id):
                    print("   ✅ 清理测试记忆成功")
                else:
                    print("   🔄 尝试清理最近的测试数据...")
                    deleted_count = memory.cleanup_test_data()
                    if deleted_count > 0:
                        print(f"   ✅ 清理了 {deleted_count} 条测试数据")
                    else:
                        print("   💡 添加功能正常，跳过清理步骤")
            except Exception as delete_error:
                print(f"   ⚠️ 清理失败: {str(delete_error)[:50]}...")
                print("   💡 添加功能正常，清理步骤可选")

            print("\n🎉 基本功能测试通过！")
        else:
            print("   ❌ 测试失败：返回格式异常")
            print(f"   📋 返回结果: {test_result}")

    except Exception as e:
        error_msg = str(e)
        print(f"   ❌ 实际测试失败: {error_msg[:150]}...")
        _print_error_solutions(error_msg)


def _print_error_solutions(error_msg):
    """打印错误解决方案."""
    print("\n   💡 可能的解决方案:")
    if "matryoshka" in error_msg.lower():
        print("   🔧 Embedding模型维度问题:")
        print("      - 当前模型不支持动态维度调整")
        print("      - 建议更换为 text-embedding-3-small 或其他兼容模型")
        print("      - 或者在环境变量中设置 EMBEDDING_MODEL=text-embedding-3-small")
    elif "vector_field" in error_msg or "mapper_parsing_exception" in error_msg:
        print("   🗄️ OpenSearch 索引问题:")
        print("      - 索引的 vector_field 配置与 embedding 维度不匹配")
        print("      - 建议删除现有索引，让系统重新创建")
        print("      - 或者手动创建正确维度的索引")
        print(f"      - 当前 embedding 维度: 1024")
    elif "connection" in error_msg.lower() or "timeout" in error_msg.lower():
        print("   🌐 网络连接问题:")
        print("      - 检查网络连接")
        print("      - 检查服务地址和端口配置")
    elif "auth" in error_msg.lower() or "401" in error_msg or "403" in error_msg:
        print("   🔑 认证问题:")
        print("      - 检查API密钥是否正确")
        print("      - 检查用户名和密码配置")
    else:
        print("   🔧 通用解决方案:")
        print("      - 检查所有环境变量配置")
        print("      - 确保所有服务正常运行")
        print("      - 检查防火墙和网络策略")

    print("\n   📝 注意: 继承方式的代码结构是正确的，问题通常出在环境配置上")


if __name__ == "__main__":
    demo_basic_functions()
